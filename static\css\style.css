/* ===================================
   ELDAWLIYA DESIGN SYSTEM v2.0
   Modern UI/UX Framework
   =================================== */

/* CSS Custom Properties (Design Tokens) */
:root {
    /* Color Palette - Light Theme */
    --primary-50: #eff6ff;
    --primary-100: #dbeafe;
    --primary-200: #bfdbfe;
    --primary-300: #93c5fd;
    --primary-400: #60a5fa;
    --primary-500: #3b82f6;
    --primary-600: #2563eb;
    --primary-700: #1d4ed8;
    --primary-800: #1e40af;
    --primary-900: #1e3a8a;

    --secondary-50: #f8fafc;
    --secondary-100: #f1f5f9;
    --secondary-200: #e2e8f0;
    --secondary-300: #cbd5e1;
    --secondary-400: #94a3b8;
    --secondary-500: #64748b;
    --secondary-600: #475569;
    --secondary-700: #334155;
    --secondary-800: #1e293b;
    --secondary-900: #0f172a;

    --success-50: #f0fdf4;
    --success-500: #22c55e;
    --success-600: #16a34a;
    --success-700: #15803d;

    --warning-50: #fffbeb;
    --warning-500: #f59e0b;
    --warning-600: #d97706;
    --warning-700: #b45309;

    --error-50: #fef2f2;
    --error-500: #ef4444;
    --error-600: #dc2626;
    --error-700: #b91c1c;

    --info-50: #f0f9ff;
    --info-500: #06b6d4;
    --info-600: #0891b2;
    --info-700: #0e7490;

    /* Neutral Colors */
    --white: #ffffff;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;

    /* Typography */
    --font-family-primary: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-family-secondary: 'Tajawal', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-family-mono: 'Fira Code', 'Courier New', monospace;

    /* Font Sizes */
    --text-xs: 0.75rem;     /* 12px */
    --text-sm: 0.875rem;    /* 14px */
    --text-base: 1rem;      /* 16px */
    --text-lg: 1.125rem;    /* 18px */
    --text-xl: 1.25rem;     /* 20px */
    --text-2xl: 1.5rem;     /* 24px */
    --text-3xl: 1.875rem;   /* 30px */
    --text-4xl: 2.25rem;    /* 36px */
    --text-5xl: 3rem;       /* 48px */

    /* Font Weights */
    --font-light: 300;
    --font-normal: 400;
    --font-medium: 500;
    --font-semibold: 600;
    --font-bold: 700;
    --font-extrabold: 800;

    /* Line Heights */
    --leading-tight: 1.25;
    --leading-normal: 1.5;
    --leading-relaxed: 1.625;
    --leading-loose: 2;

    /* Spacing Scale */
    --space-1: 0.25rem;     /* 4px */
    --space-2: 0.5rem;      /* 8px */
    --space-3: 0.75rem;     /* 12px */
    --space-4: 1rem;        /* 16px */
    --space-5: 1.25rem;     /* 20px */
    --space-6: 1.5rem;      /* 24px */
    --space-8: 2rem;        /* 32px */
    --space-10: 2.5rem;     /* 40px */
    --space-12: 3rem;       /* 48px */
    --space-16: 4rem;       /* 64px */
    --space-20: 5rem;       /* 80px */
    --space-24: 6rem;       /* 96px */

    /* Border Radius */
    --radius-sm: 0.125rem;  /* 2px */
    --radius-base: 0.25rem; /* 4px */
    --radius-md: 0.375rem;  /* 6px */
    --radius-lg: 0.5rem;    /* 8px */
    --radius-xl: 0.75rem;   /* 12px */
    --radius-2xl: 1rem;     /* 16px */
    --radius-3xl: 1.5rem;   /* 24px */
    --radius-full: 9999px;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-base: 250ms ease-in-out;
    --transition-slow: 350ms ease-in-out;

    /* Z-Index Scale */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;

    /* Current Theme Variables */
    --bg-primary: var(--white);
    --bg-secondary: var(--gray-50);
    --bg-tertiary: var(--gray-100);
    --text-primary: var(--gray-900);
    --text-secondary: var(--gray-600);
    --text-tertiary: var(--gray-500);
    --border-primary: var(--gray-200);
    --border-secondary: var(--gray-300);
}

/* Dark Theme */
[data-theme="dark"] {
    --bg-primary: var(--gray-900);
    --bg-secondary: var(--gray-800);
    --bg-tertiary: var(--gray-700);
    --text-primary: var(--white);
    --text-secondary: var(--gray-300);
    --text-tertiary: var(--gray-400);
    --border-primary: var(--gray-700);
    --border-secondary: var(--gray-600);
}

/* Base Styles */
* {
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family-primary);
    font-size: var(--text-base);
    line-height: var(--leading-normal);
    color: var(--text-primary);
    background-color: var(--bg-secondary);
    direction: rtl;
    text-align: right;
    margin: 0;
    padding: 0;
    transition: background-color var(--transition-base), color var(--transition-base);
}

/* ===================================
   TYPOGRAPHY SYSTEM
   =================================== */

h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-family-primary);
    font-weight: var(--font-semibold);
    line-height: var(--leading-tight);
    color: var(--text-primary);
    margin: 0 0 var(--space-4) 0;
}

h1 { font-size: var(--text-4xl); }
h2 { font-size: var(--text-3xl); }
h3 { font-size: var(--text-2xl); }
h4 { font-size: var(--text-xl); }
h5 { font-size: var(--text-lg); }
h6 { font-size: var(--text-base); }

p {
    margin: 0 0 var(--space-4) 0;
    color: var(--text-secondary);
    line-height: var(--leading-relaxed);
}

.text-xs { font-size: var(--text-xs); }
.text-sm { font-size: var(--text-sm); }
.text-base { font-size: var(--text-base); }
.text-lg { font-size: var(--text-lg); }
.text-xl { font-size: var(--text-xl); }
.text-2xl { font-size: var(--text-2xl); }
.text-3xl { font-size: var(--text-3xl); }

.font-light { font-weight: var(--font-light); }
.font-normal { font-weight: var(--font-normal); }
.font-medium { font-weight: var(--font-medium); }
.font-semibold { font-weight: var(--font-semibold); }
.font-bold { font-weight: var(--font-bold); }

.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-tertiary { color: var(--text-tertiary); }

/* ===================================
   MODERN CARD COMPONENT
   =================================== */

.card {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-base);
    overflow: hidden;
}

.card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.card-header {
    padding: var(--space-6);
    border-bottom: 1px solid var(--border-primary);
    background-color: var(--bg-secondary);
}

.card-body {
    padding: var(--space-6);
}

.card-footer {
    padding: var(--space-6);
    border-top: 1px solid var(--border-primary);
    background-color: var(--bg-secondary);
}

.card-title {
    font-size: var(--text-lg);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin: 0;
}

.card-subtitle {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    margin: var(--space-1) 0 0 0;
}

/* Card Variants */
.card-elevated {
    box-shadow: var(--shadow-lg);
}

.card-bordered {
    border: 2px solid var(--border-secondary);
}

.card-interactive {
    cursor: pointer;
    transition: all var(--transition-fast);
}

.card-interactive:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

/* ===================================
   MODERN BUTTON SYSTEM
   =================================== */

.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-6);
    font-family: var(--font-family-primary);
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    line-height: 1;
    text-decoration: none;
    border: 1px solid transparent;
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all var(--transition-fast);
    position: relative;
    overflow: hidden;
}

.btn:focus {
    outline: 2px solid var(--primary-500);
    outline-offset: 2px;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}

/* Button Sizes */
.btn-xs {
    padding: var(--space-1) var(--space-3);
    font-size: var(--text-xs);
}

.btn-sm {
    padding: var(--space-2) var(--space-4);
    font-size: var(--text-sm);
}

.btn-lg {
    padding: var(--space-4) var(--space-8);
    font-size: var(--text-lg);
}

.btn-xl {
    padding: var(--space-5) var(--space-10);
    font-size: var(--text-xl);
}

/* Button Variants */
.btn-primary {
    background-color: var(--primary-600);
    color: var(--white);
    border-color: var(--primary-600);
}

.btn-primary:hover {
    background-color: var(--primary-700);
    border-color: var(--primary-700);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background-color: var(--secondary-600);
    color: var(--white);
    border-color: var(--secondary-600);
}

.btn-secondary:hover {
    background-color: var(--secondary-700);
    border-color: var(--secondary-700);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-success {
    background-color: var(--success-600);
    color: var(--white);
    border-color: var(--success-600);
}

.btn-success:hover {
    background-color: var(--success-700);
    border-color: var(--success-700);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-warning {
    background-color: var(--warning-600);
    color: var(--white);
    border-color: var(--warning-600);
}

.btn-warning:hover {
    background-color: var(--warning-700);
    border-color: var(--warning-700);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-error {
    background-color: var(--error-600);
    color: var(--white);
    border-color: var(--error-600);
}

.btn-error:hover {
    background-color: var(--error-700);
    border-color: var(--error-700);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* Outline Button Variants */
.btn-outline-primary {
    background-color: transparent;
    color: var(--primary-600);
    border-color: var(--primary-600);
}

.btn-outline-primary:hover {
    background-color: var(--primary-600);
    color: var(--white);
}

.btn-outline-secondary {
    background-color: transparent;
    color: var(--secondary-600);
    border-color: var(--secondary-600);
}

.btn-outline-secondary:hover {
    background-color: var(--secondary-600);
    color: var(--white);
}

/* Ghost Button */
.btn-ghost {
    background-color: transparent;
    color: var(--text-secondary);
    border-color: transparent;
}

.btn-ghost:hover {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
}

/* ===================================
   MODERN FORM COMPONENTS
   =================================== */

.form-group {
    margin-bottom: var(--space-6);
}

.form-label {
    display: block;
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    color: var(--text-primary);
    margin-bottom: var(--space-2);
}

.form-control {
    display: block;
    width: 100%;
    padding: var(--space-3) var(--space-4);
    font-size: var(--text-base);
    font-family: var(--font-family-primary);
    line-height: var(--leading-normal);
    color: var(--text-primary);
    background-color: var(--bg-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    transition: all var(--transition-fast);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-control:disabled {
    background-color: var(--bg-tertiary);
    color: var(--text-tertiary);
    cursor: not-allowed;
}

.form-control.is-invalid {
    border-color: var(--error-500);
}

.form-control.is-invalid:focus {
    border-color: var(--error-500);
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-control.is-valid {
    border-color: var(--success-500);
}

.form-control.is-valid:focus {
    border-color: var(--success-500);
    box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
}

/* Form Control Sizes */
.form-control-sm {
    padding: var(--space-2) var(--space-3);
    font-size: var(--text-sm);
}

.form-control-lg {
    padding: var(--space-4) var(--space-6);
    font-size: var(--text-lg);
}

/* Input Groups */
.input-group {
    display: flex;
    align-items: stretch;
    width: 100%;
}

.input-group .form-control {
    border-radius: 0;
    border-left: 0;
}

.input-group .form-control:first-child {
    border-top-right-radius: var(--radius-lg);
    border-bottom-right-radius: var(--radius-lg);
    border-left: 1px solid var(--border-primary);
}

.input-group .form-control:last-child {
    border-top-left-radius: var(--radius-lg);
    border-bottom-left-radius: var(--radius-lg);
}

.input-group-text {
    display: flex;
    align-items: center;
    padding: var(--space-3) var(--space-4);
    font-size: var(--text-base);
    font-weight: var(--font-normal);
    color: var(--text-secondary);
    background-color: var(--bg-tertiary);
    border: 1px solid var(--border-primary);
    border-left: 0;
}

.input-group-text:first-child {
    border-top-right-radius: var(--radius-lg);
    border-bottom-right-radius: var(--radius-lg);
    border-left: 1px solid var(--border-primary);
}

.input-group-text:last-child {
    border-top-left-radius: var(--radius-lg);
    border-bottom-left-radius: var(--radius-lg);
}

/* Form Feedback */
.invalid-feedback {
    display: block;
    width: 100%;
    margin-top: var(--space-1);
    font-size: var(--text-sm);
    color: var(--error-600);
}

.valid-feedback {
    display: block;
    width: 100%;
    margin-top: var(--space-1);
    font-size: var(--text-sm);
    color: var(--success-600);
}

/* ===================================
   MODERN TABLE COMPONENT
   =================================== */

.table-container {
    background-color: var(--bg-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
}

.table {
    width: 100%;
    margin-bottom: 0;
    border-collapse: collapse;
}

.table th,
.table td {
    padding: var(--space-4);
    text-align: right;
    border-bottom: 1px solid var(--border-primary);
    vertical-align: middle;
}

.table th {
    background-color: var(--bg-secondary);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    font-size: var(--text-sm);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.table tbody tr {
    transition: background-color var(--transition-fast);
}

.table tbody tr:hover {
    background-color: var(--bg-secondary);
}

.table tbody tr:last-child td {
    border-bottom: none;
}

/* Table Variants */
.table-striped tbody tr:nth-child(odd) {
    background-color: var(--bg-secondary);
}

.table-bordered {
    border: 1px solid var(--border-primary);
}

.table-bordered th,
.table-bordered td {
    border: 1px solid var(--border-primary);
}

/* Responsive Table */
.table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

@media (max-width: 768px) {
    .table-responsive .table {
        font-size: var(--text-sm);
    }

    .table-responsive .table th,
    .table-responsive .table td {
        padding: var(--space-2) var(--space-3);
    }
}

/* ===================================
   MODERN NAVIGATION SYSTEM
   =================================== */

.navbar {
    background-color: var(--bg-primary);
    border-bottom: 1px solid var(--border-primary);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-base);
    position: sticky;
    top: 0;
    z-index: var(--z-sticky);
}

.navbar-brand {
    font-size: var(--text-xl);
    font-weight: var(--font-bold);
    color: var(--text-primary);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.navbar-brand:hover {
    color: var(--primary-600);
}

.nav-link {
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: var(--font-medium);
    padding: var(--space-2) var(--space-4);
    border-radius: var(--radius-lg);
    transition: all var(--transition-fast);
    position: relative;
}

.nav-link:hover {
    color: var(--primary-600);
    background-color: var(--primary-50);
}

.nav-link.active {
    color: var(--primary-600);
    background-color: var(--primary-100);
}

/* Sidebar Navigation */
.sidebar {
    background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-800) 100%);
    color: var(--white);
    transition: all var(--transition-base);
    box-shadow: var(--shadow-lg);
}

.sidebar-header {
    padding: var(--space-6);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-brand {
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.sidebar-brand-icon {
    width: 40px;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-lg);
}

.sidebar-brand-title {
    font-size: var(--text-xl);
    font-weight: var(--font-bold);
    margin: 0;
}

.sidebar-brand-subtitle {
    font-size: var(--text-sm);
    opacity: 0.8;
    margin: 0;
}

.sidebar-menu {
    list-style: none;
    padding: var(--space-4) 0;
    margin: 0;
}

.sidebar-menu-item {
    margin: var(--space-1) var(--space-4);
}

.sidebar-menu-link {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    padding: var(--space-3) var(--space-4);
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    border-radius: var(--radius-lg);
    transition: all var(--transition-fast);
    font-weight: var(--font-medium);
}

.sidebar-menu-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--white);
    transform: translateX(-2px);
}

.sidebar-menu-link.active {
    background-color: rgba(255, 255, 255, 0.15);
    color: var(--white);
}

.sidebar-menu-icon {
    width: 20px;
    text-align: center;
    font-size: var(--text-base);
}

/* ===================================
   ALERT SYSTEM
   =================================== */

.alert {
    padding: var(--space-4) var(--space-6);
    border-radius: var(--radius-lg);
    border: 1px solid transparent;
    margin-bottom: var(--space-4);
    position: relative;
    display: flex;
    align-items: flex-start;
    gap: var(--space-3);
}

.alert-icon {
    flex-shrink: 0;
    width: 20px;
    height: 20px;
    margin-top: 2px;
}

.alert-content {
    flex: 1;
}

.alert-title {
    font-weight: var(--font-semibold);
    margin: 0 0 var(--space-1) 0;
}

.alert-message {
    margin: 0;
    font-size: var(--text-sm);
}

.alert-close {
    background: none;
    border: none;
    color: inherit;
    cursor: pointer;
    padding: 0;
    margin-right: var(--space-2);
    opacity: 0.7;
    transition: opacity var(--transition-fast);
}

.alert-close:hover {
    opacity: 1;
}

/* Alert Variants */
.alert-success {
    background-color: var(--success-50);
    border-color: var(--success-200);
    color: var(--success-800);
}

.alert-warning {
    background-color: var(--warning-50);
    border-color: var(--warning-200);
    color: var(--warning-800);
}

.alert-error {
    background-color: var(--error-50);
    border-color: var(--error-200);
    color: var(--error-800);
}

.alert-info {
    background-color: var(--info-50);
    border-color: var(--info-200);
    color: var(--info-800);
}

/* ===================================
   UTILITY CLASSES
   =================================== */

/* Spacing */
.m-0 { margin: 0; }
.m-1 { margin: var(--space-1); }
.m-2 { margin: var(--space-2); }
.m-3 { margin: var(--space-3); }
.m-4 { margin: var(--space-4); }
.m-5 { margin: var(--space-5); }
.m-6 { margin: var(--space-6); }
.m-8 { margin: var(--space-8); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--space-1); }
.mt-2 { margin-top: var(--space-2); }
.mt-3 { margin-top: var(--space-3); }
.mt-4 { margin-top: var(--space-4); }
.mt-5 { margin-top: var(--space-5); }
.mt-6 { margin-top: var(--space-6); }
.mt-8 { margin-top: var(--space-8); }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--space-1); }
.mb-2 { margin-bottom: var(--space-2); }
.mb-3 { margin-bottom: var(--space-3); }
.mb-4 { margin-bottom: var(--space-4); }
.mb-5 { margin-bottom: var(--space-5); }
.mb-6 { margin-bottom: var(--space-6); }
.mb-8 { margin-bottom: var(--space-8); }

.p-0 { padding: 0; }
.p-1 { padding: var(--space-1); }
.p-2 { padding: var(--space-2); }
.p-3 { padding: var(--space-3); }
.p-4 { padding: var(--space-4); }
.p-5 { padding: var(--space-5); }
.p-6 { padding: var(--space-6); }
.p-8 { padding: var(--space-8); }

/* Display */
.d-none { display: none; }
.d-block { display: block; }
.d-inline { display: inline; }
.d-inline-block { display: inline-block; }
.d-flex { display: flex; }
.d-grid { display: grid; }

/* Flexbox */
.flex-row { flex-direction: row; }
.flex-col { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }
.justify-start { justify-content: flex-start; }
.justify-center { justify-content: center; }
.justify-end { justify-content: flex-end; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }
.items-start { align-items: flex-start; }
.items-center { align-items: center; }
.items-end { align-items: flex-end; }
.items-stretch { align-items: stretch; }

/* Text Alignment */
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-justify { text-align: justify; }

/* Border Radius */
.rounded-none { border-radius: 0; }
.rounded-sm { border-radius: var(--radius-sm); }
.rounded { border-radius: var(--radius-base); }
.rounded-md { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-2xl { border-radius: var(--radius-2xl); }
.rounded-full { border-radius: var(--radius-full); }

/* Shadows */
.shadow-none { box-shadow: none; }
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow { box-shadow: var(--shadow-base); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }

/* Transitions */
.transition { transition: all var(--transition-base); }
.transition-fast { transition: all var(--transition-fast); }
.transition-slow { transition: all var(--transition-slow); }

/* ===================================
   RESPONSIVE GRID SYSTEM
   =================================== */

/* Modern CSS Grid Layout */
.grid {
    display: grid;
    gap: var(--space-4);
}

.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
.grid-cols-5 { grid-template-columns: repeat(5, minmax(0, 1fr)); }
.grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }
.grid-cols-12 { grid-template-columns: repeat(12, minmax(0, 1fr)); }

/* Grid Column Spans */
.col-span-1 { grid-column: span 1 / span 1; }
.col-span-2 { grid-column: span 2 / span 2; }
.col-span-3 { grid-column: span 3 / span 3; }
.col-span-4 { grid-column: span 4 / span 4; }
.col-span-5 { grid-column: span 5 / span 5; }
.col-span-6 { grid-column: span 6 / span 6; }
.col-span-7 { grid-column: span 7 / span 7; }
.col-span-8 { grid-column: span 8 / span 8; }
.col-span-9 { grid-column: span 9 / span 9; }
.col-span-10 { grid-column: span 10 / span 10; }
.col-span-11 { grid-column: span 11 / span 11; }
.col-span-12 { grid-column: span 12 / span 12; }
.col-span-full { grid-column: 1 / -1; }

/* Grid Gaps */
.gap-0 { gap: 0; }
.gap-1 { gap: var(--space-1); }
.gap-2 { gap: var(--space-2); }
.gap-3 { gap: var(--space-3); }
.gap-4 { gap: var(--space-4); }
.gap-5 { gap: var(--space-5); }
.gap-6 { gap: var(--space-6); }
.gap-8 { gap: var(--space-8); }

/* Auto-fit Grid */
.grid-auto-fit {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.grid-auto-fill {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
}

/* Responsive Grid Breakpoints */
@media (min-width: 576px) {
    .sm\:grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
    .sm\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
    .sm\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
    .sm\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
    .sm\:grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }
    .sm\:grid-cols-12 { grid-template-columns: repeat(12, minmax(0, 1fr)); }

    .sm\:col-span-1 { grid-column: span 1 / span 1; }
    .sm\:col-span-2 { grid-column: span 2 / span 2; }
    .sm\:col-span-3 { grid-column: span 3 / span 3; }
    .sm\:col-span-4 { grid-column: span 4 / span 4; }
    .sm\:col-span-6 { grid-column: span 6 / span 6; }
    .sm\:col-span-12 { grid-column: span 12 / span 12; }
    .sm\:col-span-full { grid-column: 1 / -1; }
}

@media (min-width: 768px) {
    .md\:grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
    .md\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
    .md\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
    .md\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
    .md\:grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }
    .md\:grid-cols-12 { grid-template-columns: repeat(12, minmax(0, 1fr)); }

    .md\:col-span-1 { grid-column: span 1 / span 1; }
    .md\:col-span-2 { grid-column: span 2 / span 2; }
    .md\:col-span-3 { grid-column: span 3 / span 3; }
    .md\:col-span-4 { grid-column: span 4 / span 4; }
    .md\:col-span-6 { grid-column: span 6 / span 6; }
    .md\:col-span-8 { grid-column: span 8 / span 8; }
    .md\:col-span-12 { grid-column: span 12 / span 12; }
    .md\:col-span-full { grid-column: 1 / -1; }
}

@media (min-width: 992px) {
    .lg\:grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
    .lg\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
    .lg\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
    .lg\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
    .lg\:grid-cols-5 { grid-template-columns: repeat(5, minmax(0, 1fr)); }
    .lg\:grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }
    .lg\:grid-cols-12 { grid-template-columns: repeat(12, minmax(0, 1fr)); }

    .lg\:col-span-1 { grid-column: span 1 / span 1; }
    .lg\:col-span-2 { grid-column: span 2 / span 2; }
    .lg\:col-span-3 { grid-column: span 3 / span 3; }
    .lg\:col-span-4 { grid-column: span 4 / span 4; }
    .lg\:col-span-5 { grid-column: span 5 / span 5; }
    .lg\:col-span-6 { grid-column: span 6 / span 6; }
    .lg\:col-span-8 { grid-column: span 8 / span 8; }
    .lg\:col-span-9 { grid-column: span 9 / span 9; }
    .lg\:col-span-10 { grid-column: span 10 / span 10; }
    .lg\:col-span-12 { grid-column: span 12 / span 12; }
    .lg\:col-span-full { grid-column: 1 / -1; }
}

/* ===================================
   RESPONSIVE FLEXBOX UTILITIES
   =================================== */

/* Responsive Flex Direction */
@media (max-width: 767.98px) {
    .flex-mobile-col { flex-direction: column !important; }
    .flex-mobile-row { flex-direction: row !important; }
}

@media (min-width: 768px) {
    .flex-md-row { flex-direction: row !important; }
    .flex-md-col { flex-direction: column !important; }
}

@media (min-width: 992px) {
    .flex-lg-row { flex-direction: row !important; }
    .flex-lg-col { flex-direction: column !important; }
}

/* Responsive Justify Content */
@media (max-width: 767.98px) {
    .justify-mobile-center { justify-content: center !important; }
    .justify-mobile-start { justify-content: flex-start !important; }
    .justify-mobile-end { justify-content: flex-end !important; }
}

/* ===================================
   RESPONSIVE SPACING
   =================================== */

/* Responsive Margins */
@media (max-width: 767.98px) {
    .m-mobile-0 { margin: 0 !important; }
    .m-mobile-2 { margin: var(--space-2) !important; }
    .m-mobile-4 { margin: var(--space-4) !important; }
    .mt-mobile-0 { margin-top: 0 !important; }
    .mt-mobile-2 { margin-top: var(--space-2) !important; }
    .mt-mobile-4 { margin-top: var(--space-4) !important; }
    .mb-mobile-0 { margin-bottom: 0 !important; }
    .mb-mobile-2 { margin-bottom: var(--space-2) !important; }
    .mb-mobile-4 { margin-bottom: var(--space-4) !important; }
}

/* Responsive Padding */
@media (max-width: 767.98px) {
    .p-mobile-0 { padding: 0 !important; }
    .p-mobile-2 { padding: var(--space-2) !important; }
    .p-mobile-4 { padding: var(--space-4) !important; }
    .px-mobile-2 { padding-left: var(--space-2) !important; padding-right: var(--space-2) !important; }
    .px-mobile-4 { padding-left: var(--space-4) !important; padding-right: var(--space-4) !important; }
    .py-mobile-2 { padding-top: var(--space-2) !important; padding-bottom: var(--space-2) !important; }
    .py-mobile-4 { padding-top: var(--space-4) !important; padding-bottom: var(--space-4) !important; }
}

/* ===================================
   RESPONSIVE COMPONENTS
   =================================== */

/* Responsive Cards */
@media (max-width: 767.98px) {
    .card {
        margin-bottom: var(--space-4);
    }

    .card-body {
        padding: var(--space-4);
    }

    .card-header,
    .card-footer {
        padding: var(--space-4);
    }
}

/* Responsive Tables */
@media (max-width: 991.98px) {
    .table-responsive-stack {
        display: block;
    }

    .table-responsive-stack .table,
    .table-responsive-stack .table thead,
    .table-responsive-stack .table tbody,
    .table-responsive-stack .table th,
    .table-responsive-stack .table td,
    .table-responsive-stack .table tr {
        display: block;
    }

    .table-responsive-stack .table thead tr {
        position: absolute;
        top: -9999px;
        left: -9999px;
    }

    .table-responsive-stack .table tr {
        border: 1px solid var(--border-primary);
        margin-bottom: var(--space-2);
        border-radius: var(--radius-lg);
        padding: var(--space-3);
    }

    .table-responsive-stack .table td {
        border: none;
        position: relative;
        padding-right: 50% !important;
        padding-left: var(--space-3) !important;
        text-align: right;
    }

    .table-responsive-stack .table td:before {
        content: attr(data-label) ": ";
        position: absolute;
        right: var(--space-3);
        width: 45%;
        text-align: right;
        font-weight: var(--font-semibold);
        color: var(--text-primary);
    }
}

/* Responsive Navigation */
@media (max-width: 991.98px) {
    .sidebar {
        transform: translateX(-100%);
        transition: transform var(--transition-base);
        position: fixed;
        top: 0;
        right: 0;
        height: 100vh;
        width: 280px;
        z-index: var(--z-modal);
    }

    .sidebar.show {
        transform: translateX(0);
    }

    .sidebar-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: var(--z-modal-backdrop);
        opacity: 0;
        visibility: hidden;
        transition: all var(--transition-base);
    }

    .sidebar-overlay.show {
        opacity: 1;
        visibility: visible;
    }
}