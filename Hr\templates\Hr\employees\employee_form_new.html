{% extends 'Hr/base_hr.html' %}
{% load static %}

{% block title %}{{ title }} - نظام الدولية{% endblock %}

{% block extra_css %}
<style>
    /* إعدادات التصميم لنموذج الموظف */
    .accordion-button:not(.collapsed) {
        background-color: rgba(13, 110, 253, 0.1);
        color: #0d6efd;
    }
    
    .accordion-button:focus {
        box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    }
    
    /* منع توسع حقل الجنسية عند المرور عليه */
    .nationality-select {
        width: 100% !important;
        appearance: menulist !important;
        -webkit-appearance: menulist !important;
        -moz-appearance: menulist !important;
        overflow: hidden !important;
    }
    
    .nationality-select:hover,
    .nationality-select:focus,
    .nationality-select option {
        width: 100% !important;
        max-width: 100% !important;
    }
</style>
{% endblock %}

{% block page_title %}{{ title }}{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounts:home' %}">الرئيسية</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:list' %}">الموظفين</a></li>
<li class="breadcrumb-item active">{{ title }}</li>
{% endblock %}

{% block content %}
<div class="section-spacing">
    <div class="card">
        <div class="card-header">
            <div class="card-header-content">
                <div class="card-title">
                    <i class="fas fa-user-plus"></i>
                    <span>{{ title }}</span>
                </div>
                <div class="card-subtitle">إدخال بيانات موظف جديد</div>
            </div>
            <div class="card-actions">
                <a href="{% url 'Hr:list' %}" class="btn btn-outline-secondary btn-with-icon">
                    <i class="fas fa-arrow-right"></i>
                    <span>العودة للقائمة</span>
                </a>
            </div>
        </div>
        <div class="card-body">
            <form method="post" enctype="multipart/form-data" class="needs-validation" novalidate>
                {% csrf_token %}

                {% if form.non_field_errors %}
                <div class="alert alert-danger mb-4" role="alert">
                    {% for error in form.non_field_errors %}
                    <p class="mb-0">{{ error }}</p>
                    {% endfor %}
                </div>
                {% endif %}
            
                <!-- تنظيم الحقول في أقسام -->
                <div class="space-y-6">
                    <!-- المعلومات الأساسية -->
                    <div class="card">
                        <div class="card-header">
                            <div class="card-header-content">
                                <div class="card-title">
                                    <i class="fas fa-user"></i>
                                    <span>المعلومات الأساسية</span>
                                </div>
                                <div class="card-subtitle">البيانات الشخصية الأساسية للموظف</div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div class="form-group">
                                    <label for="{{ form.emp_id.id_for_label }}" class="form-label">{{ form.emp_id.label }}</label>
                                    {{ form.emp_id }}
                                    {% if form.emp_id.errors %}
                                    <div class="form-error">{{ form.emp_id.errors }}</div>
                                    {% endif %}
                                </div>
                                <div class="form-group">
                                    <label for="{{ form.emp_first_name.id_for_label }}" class="form-label">{{ form.emp_first_name.label }}</label>
                                    {{ form.emp_first_name }}
                                    {% if form.emp_first_name.errors %}
                                    <div class="form-error">{{ form.emp_first_name.errors }}</div>
                                    {% endif %}
                                </div>
                                <div class="form-group">
                                    <label for="{{ form.emp_second_name.id_for_label }}" class="form-label">{{ form.emp_second_name.label }}</label>
                                    {{ form.emp_second_name }}
                                    {% if form.emp_second_name.errors %}
                                    <div class="form-error">{{ form.emp_second_name.errors }}</div>
                                    {% endif %}
                                </div>
                                <div class="form-group">
                                    <label for="{{ form.emp_full_name.id_for_label }}" class="form-label">{{ form.emp_full_name.label }}</label>
                                    {{ form.emp_full_name }}
                                    {% if form.emp_full_name.errors %}
                                    <div class="form-error">{{ form.emp_full_name.errors }}</div>
                                    {% endif %}
                                </div>
                                <div class="form-group">
                                    <label for="{{ form.emp_name_english.id_for_label }}" class="form-label">{{ form.emp_name_english.label }}</label>
                                    {{ form.emp_name_english }}
                                    {% if form.emp_name_english.errors %}
                                    <div class="form-error">{{ form.emp_name_english.errors }}</div>
                                    {% endif %}
                                </div>
                                <div class="form-group">
                                    <label for="{{ form.mother_name.id_for_label }}" class="form-label">{{ form.mother_name.label }}</label>
                                    {{ form.mother_name }}
                                    {% if form.mother_name.errors %}
                                    <div class="form-error">{{ form.mother_name.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- معلومات الهوية -->
                    <div class="card">
                        <div class="card-header">
                            <div class="card-header-content">
                                <div class="card-title">
                                    <i class="fas fa-id-card"></i>
                                    <span>معلومات الهوية</span>
                                </div>
                                <div class="card-subtitle">بيانات الهوية والجنسية</div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div class="form-group">
                                    <label for="{{ form.national_id.id_for_label }}" class="form-label">{{ form.national_id.label }}</label>
                                    {{ form.national_id }}
                                    {% if form.national_id.errors %}
                                    <div class="form-error">{{ form.national_id.errors }}</div>
                                    {% endif %}
                                </div>
                                <div class="form-group">
                                    <label for="{{ form.date_birth.id_for_label }}" class="form-label">{{ form.date_birth.label }}</label>
                                    {{ form.date_birth }}
                                    {% if form.date_birth.errors %}
                                    <div class="form-error">{{ form.date_birth.errors }}</div>
                                    {% endif %}
                                </div>
                                <div class="form-group">
                                    <label for="{{ form.place_birth.id_for_label }}" class="form-label">{{ form.place_birth.label }}</label>
                                    {{ form.place_birth }}
                                    {% if form.place_birth.errors %}
                                    <div class="form-error">{{ form.place_birth.errors }}</div>
                                    {% endif %}
                                </div>
                                <div class="form-group">
                                    <label for="{{ form.emp_nationality.id_for_label }}" class="form-label">{{ form.emp_nationality.label }}</label>
                                    <select name="{{ form.emp_nationality.html_name }}" id="{{ form.emp_nationality.auto_id }}" class="form-select nationality-select {% if form.emp_nationality.errors %}is-invalid{% endif %}" {% if form.emp_nationality.field.required %}required{% endif %}>
                                        <option value="">اختر الجنسية</option>
                                        <option value="مصري">مصري</option>
                                        <option value="سوري">سوري</option>
                                        <option value="فلسطيني">فلسطيني</option>
                                        <option value="سوداني">سوداني</option>
                                        <option value="يمني">يمني</option>
                                        <option value="أردني">أردني</option>
                                        <option value="لبناني">لبناني</option>
                                        <option value="عراقي">عراقي</option>
                                        <option value="سعودي">سعودي</option>
                                        <option value="إماراتي">إماراتي</option>
                                        <option value="كويتي">كويتي</option>
                                        <option value="بحريني">بحريني</option>
                                        <option value="قطري">قطري</option>
                                        <option value="عماني">عماني</option>
                                        <option value="ليبي">ليبي</option>
                                        <option value="تونسي">تونسي</option>
                                        <option value="جزائري">جزائري</option>
                                        <option value="مغربي">مغربي</option>
                                        <option value="موريتاني">موريتاني</option>
                                    </select>
                                    {% if form.emp_nationality.errors %}
                                    <div class="form-error">{{ form.emp_nationality.errors }}</div>
                                    {% endif %}
                                </div>
                                <div class="form-group">
                                    <label for="{{ form.emp_marital_status.id_for_label }}" class="form-label">{{ form.emp_marital_status.label }}</label>
                                    {{ form.emp_marital_status }}
                                    {% if form.emp_marital_status.errors %}
                                    <div class="form-error">{{ form.emp_marital_status.errors }}</div>
                                    {% endif %}
                                </div>
                                <div class="form-group">
                                    <label for="{{ form.military_service_certificate.id_for_label }}" class="form-label">{{ form.military_service_certificate.label }}</label>
                                    {{ form.military_service_certificate }}
                                    {% if form.military_service_certificate.errors %}
                                    <div class="form-error">{{ form.military_service_certificate.errors }}</div>
                                    {% endif %}
                                </div>
                                <div class="form-group">
                                    <div class="form-check">
                                        {{ form.people_with_special_needs }}
                                        <label class="form-check-label" for="{{ form.people_with_special_needs.id_for_label }}">
                                            {{ form.people_with_special_needs.label }}
                                        </label>
                                        {% if form.people_with_special_needs.errors %}
                                        <div class="form-error">{{ form.people_with_special_needs.errors }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- بيانات الاتصال -->
                    <div class="card">
                        <div class="card-header">
                            <div class="card-header-content">
                                <div class="card-title">
                                    <i class="fas fa-phone"></i>
                                    <span>بيانات الاتصال</span>
                                </div>
                                <div class="card-subtitle">معلومات الاتصال والعنوان</div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div class="form-group">
                                    <label for="{{ form.emp_phone1.id_for_label }}" class="form-label">{{ form.emp_phone1.label }}</label>
                                    {{ form.emp_phone1 }}
                                    {% if form.emp_phone1.errors %}
                                    <div class="form-error">{{ form.emp_phone1.errors }}</div>
                                    {% endif %}
                                </div>
                                <div class="form-group">
                                    <label for="{{ form.emp_phone2.id_for_label }}" class="form-label">{{ form.emp_phone2.label }}</label>
                                    {{ form.emp_phone2 }}
                                    {% if form.emp_phone2.errors %}
                                    <div class="invalid-feedback d-block">{{ form.emp_phone2.errors }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.emp_address.id_for_label }}" class="form-label">{{ form.emp_address.label }}</label>
                                    {{ form.emp_address }}
                                    {% if form.emp_address.errors %}
                                    <div class="invalid-feedback d-block">{{ form.emp_address.errors }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.governorate.id_for_label }}" class="form-label">{{ form.governorate.label }}</label>
                                    {{ form.governorate }}
                                    {% if form.governorate.errors %}
                                    <div class="invalid-feedback d-block">{{ form.governorate.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- معلومات العمل -->
                <div class="accordion-item">
                    <h2 class="accordion-header" id="heading4">
                        <button class="accordion-button collapsed" type="button"
                                data-bs-toggle="collapse" data-bs-target="#collapse4"
                                aria-expanded="false" aria-controls="collapse4">
                            معلومات العمل
                        </button>
                    </h2>
                    <div id="collapse4" class="accordion-collapse collapse"
                         aria-labelledby="heading4" data-bs-parent="#employeeFormAccordion">
                        <div class="accordion-body">
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.working_condition.id_for_label }}" class="form-label">{{ form.working_condition.label }}</label>
                                    {{ form.working_condition }}
                                    {% if form.working_condition.errors %}
                                    <div class="invalid-feedback d-block">{{ form.working_condition.errors }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.department.id_for_label }}" class="form-label">{{ form.department.label }}</label>
                                    {{ form.department }}
                                    {% if form.department.errors %}
                                    <div class="invalid-feedback d-block">{{ form.department.errors }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.jop_name.id_for_label }}" class="form-label">{{ form.jop_name.label }}</label>
                                    {{ form.jop_name }}
                                    {% if form.jop_name.errors %}
                                    <div class="invalid-feedback d-block">{{ form.jop_name.errors }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.emp_date_hiring.id_for_label }}" class="form-label">{{ form.emp_date_hiring.label }}</label>
                                    {{ form.emp_date_hiring }}
                                    {% if form.emp_date_hiring.errors %}
                                    <div class="invalid-feedback d-block">{{ form.emp_date_hiring.errors }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.emp_type.id_for_label }}" class="form-label">{{ form.emp_type.label }}</label>
                                    {{ form.emp_type }}
                                    {% if form.emp_type.errors %}
                                    <div class="invalid-feedback d-block">{{ form.emp_type.errors }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.shift_type.id_for_label }}" class="form-label">{{ form.shift_type.label }}</label>
                                    {{ form.shift_type }}
                                    {% if form.shift_type.errors %}
                                    <div class="invalid-feedback d-block">{{ form.shift_type.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- معلومات التأمين والصحة -->
                <div class="accordion-item">
                    <h2 class="accordion-header" id="heading5">
                        <button class="accordion-button collapsed" type="button"
                                data-bs-toggle="collapse" data-bs-target="#collapse5"
                                aria-expanded="false" aria-controls="collapse5">
                            معلومات التأمين والصحة
                        </button>
                    </h2>
                    <div id="collapse5" class="accordion-collapse collapse"
                         aria-labelledby="heading5" data-bs-parent="#employeeFormAccordion">
                        <div class="accordion-body">
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.insurance_status.id_for_label }}" class="form-label">{{ form.insurance_status.label }}</label>
                                    {{ form.insurance_status }}
                                    {% if form.insurance_status.errors %}
                                    <div class="invalid-feedback d-block">{{ form.insurance_status.errors }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.insurance_salary.id_for_label }}" class="form-label">{{ form.insurance_salary.label }}</label>
                                    {{ form.insurance_salary }}
                                    {% if form.insurance_salary.errors %}
                                    <div class="invalid-feedback d-block">{{ form.insurance_salary.errors }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.health_card.id_for_label }}" class="form-label">{{ form.health_card.label }}</label>
                                    {{ form.health_card }}
                                    {% if form.health_card.errors %}
                                    <div class="invalid-feedback d-block">{{ form.health_card.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الصورة والمرفقات -->
                <div class="accordion-item">
                    <h2 class="accordion-header" id="heading6">
                        <button class="accordion-button collapsed" type="button"
                                data-bs-toggle="collapse" data-bs-target="#collapse6"
                                aria-expanded="false" aria-controls="collapse6">
                            الصورة والمرفقات
                        </button>
                    </h2>
                    <div id="collapse6" class="accordion-collapse collapse"
                         aria-labelledby="heading6" data-bs-parent="#employeeFormAccordion">
                        <div class="accordion-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.emp_image.id_for_label }}" class="form-label">{{ form.emp_image.label }}</label>
                                    {{ form.emp_image }}
                                    {% if form.emp_image.errors %}
                                    <div class="invalid-feedback d-block">{{ form.emp_image.errors }}</div>
                                    {% endif %}
                                    <div class="form-text">يُفضل رفع صورة بحجم 300x300 بكسل</div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.age.id_for_label }}" class="form-label">{{ form.age.label }}</label>
                                    {{ form.age }}
                                    {% if form.age.errors %}
                                    <div class="invalid-feedback d-block">{{ form.age.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

                <div class="form-actions">
                    <a href="{% url 'Hr:employees:list' %}" class="btn btn-outline-secondary btn-with-icon">
                        <i class="fas fa-times"></i>
                        <span>إلغاء</span>
                    </a>
                    <button type="submit" class="btn btn-primary btn-with-icon">
                        <i class="fas fa-save"></i>
                        <span>{{ button_text|default:"حفظ" }}</span>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تحقق من صحة النماذج
        (function () {
            'use strict'

            // أشكال تحتاج إلى التحقق من صحتها
            var forms = document.querySelectorAll('.needs-validation')

            // حلقة عليهم ومنع الإرسال
            Array.prototype.slice.call(forms)
                .forEach(function (form) {
                    form.addEventListener('submit', function (event) {
                        if (!form.checkValidity()) {
                            event.preventDefault()
                            event.stopPropagation()

                            // العثور على أول حقل غير صحيح والتركيز عليه
                            var firstInvalidField = form.querySelector(':invalid')
                            if (firstInvalidField) {
                                // فتح الأكورديون الذي يحتوي على الحقل
                                var accordionItem = firstInvalidField.closest('.accordion-collapse')
                                if (accordionItem && !accordionItem.classList.contains('show')) {
                                    var button = document.querySelector('[data-bs-target="#' + accordionItem.id + '"]')
                                    if (button) {
                                        button.click()
                                    }
                                }

                                // التركيز على الحقل بعد فترة قصيرة للسماح للأكورديون بالفتح
                                setTimeout(function() {
                                    firstInvalidField.focus()
                                }, 300)
                            }
                        }

                        form.classList.add('was-validated')
                    }, false)
                })
        })()

        // تحديث الاسم الكامل تلقائياً
        var firstNameField = document.getElementById('id_emp_first_name')
        var secondNameField = document.getElementById('id_emp_second_name')
        var fullNameField = document.getElementById('id_emp_full_name')

        function updateFullName() {
            if (firstNameField && secondNameField && fullNameField) {
                var firstName = firstNameField.value.trim()
                var secondName = secondNameField.value.trim()
                if (firstName || secondName) {
                    fullNameField.value = (firstName + ' ' + secondName).trim()
                }
            }
        }

        if (firstNameField) firstNameField.addEventListener('input', updateFullName)
        if (secondNameField) secondNameField.addEventListener('input', updateFullName)

        // تحديد الجنسية المحفوظة
        var nationalityField = document.getElementById('id_emp_nationality')
        if (nationalityField && nationalityField.dataset.value) {
            nationalityField.value = nationalityField.dataset.value
        }

        // تحسين تجربة المستخدم للحقول المطلوبة
        var requiredFields = document.querySelectorAll('[required]')
        requiredFields.forEach(function(field) {
            field.addEventListener('blur', function() {
                if (this.value.trim() === '') {
                    this.classList.add('is-invalid')
                } else {
                    this.classList.remove('is-invalid')
                    this.classList.add('is-valid')
                }
            })

            field.addEventListener('input', function() {
                if (this.classList.contains('is-invalid') && this.value.trim() !== '') {
                    this.classList.remove('is-invalid')
                    this.classList.add('is-valid')
                }
            })
        })
    });
</script>
{% endblock %}
