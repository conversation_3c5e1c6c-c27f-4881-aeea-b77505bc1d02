{% extends 'Hr/base_hr.html' %}
{% load static %}
{% load image_utils %}

{% block title %}تفاصيل القسم - {{ department.dept_name }} - نظام الدولية{% endblock %}

{% block page_title %}تفاصيل القسم: {{ department.dept_name }}{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounts:home' %}">الرئيسية</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:dashboard' %}">الموارد البشرية</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:departments:list' %}">الأقسام</a></li>
<li class="breadcrumb-item active">{{ department.dept_name }}</li>
{% endblock %}

{% block content %}
<div class="section-spacing">
    <!-- Page Header -->
    <div class="page-header">
        <div class="page-header-content">
            <div class="page-title">
                <i class="fas fa-building"></i>
                <span>{{ department.dept_name }}</span>
            </div>
            <div class="page-subtitle">تفاصيل القسم ومعلومات الموظفين</div>
        </div>
        <div class="page-actions">
            <a href="{% url 'Hr:departments:edit' department.dept_code %}" class="btn btn-primary btn-with-icon">
                <i class="fas fa-edit"></i>
                <span>تعديل</span>
            </a>
            <a href="{% url 'Hr:departments:delete' department.dept_code %}" class="btn btn-outline-danger btn-with-icon delete-btn" data-bs-toggle="modal" data-bs-target="#deleteModal" data-name="{{ department.dept_name }}" data-url="{% url 'Hr:departments:delete' department.dept_code %}">
                <i class="fas fa-trash-alt"></i>
                <span>حذف</span>
            </a>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Department Information -->
        <div class="lg:col-span-2">
            <div class="card">
                <div class="card-header">
                    <div class="card-header-content">
                        <div class="card-title">
                            <i class="fas fa-info-circle"></i>
                            <span>معلومات القسم</span>
                        </div>
                        <div class="card-subtitle">البيانات الأساسية للقسم</div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                        <div class="info-item">
                            <div class="info-label">كود القسم</div>
                            <div class="info-value">{{ department.dept_code }}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">تاريخ الإنشاء</div>
                            <div class="info-value">{{ department.created_at|date:"Y-m-d" }}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">عدد الموظفين</div>
                            <div class="info-value">{{ employee_count }} موظف</div>
                        </div>
                            </div>

                            {% if manager %}
                            <div class="row mb-3 border-top pt-3">
                                <div class="col-12">
                                    <p class="text-muted mb-2">مدير القسم</p>
                                    <div class="d-flex align-items-center">
                                        {% if manager.emp_image %}
                                        <img src="{{ manager.emp_image.url }}" alt="{{ manager.emp_full_name }}" class="rounded-circle me-3 object-fit-cover" width="60" height="60">
                                        {% else %}
                                        <div class="avatar bg-primary text-white me-3">
                                            {{ manager.emp_first_name|slice:":1"|upper }}
                                        </div>
                                        {% endif %}
                                        <div>
                                            <h6 class="mb-1">{{ manager.emp_full_name }}</h6>
                                            <p class="text-muted small mb-0">
                                                <i class="fas fa-briefcase me-1"></i>{{ manager.jop_name|default:"غير محدد" }} |
                                                <i class="fas fa-phone me-1"></i>{{ manager.emp_phone1|default:"غير محدد" }}
                                            </p>
                                            <a href="{% url 'Hr:employees:detail' manager.emp_id %}" class="btn btn-sm btn-outline-primary mt-2">
                                                <i class="fas fa-user me-1"></i> عرض الملف الشخصي
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endif %}

                            {% if department.note %}
                            <div class="row mb-3 border-top pt-3">
                                <div class="col-12">
                                    <p class="text-muted mb-2">ملاحظات</p>
                                    <p class="mb-0">{{ department.note }}</p>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="col-md-5">
                        <!-- Employee Count by Status -->
                        <div class="mb-4">
                            <h6 class="border-bottom pb-2 mb-3">توزيع الموظفين حسب الحالة</h6>
                            <div class="chart-container" style="height: 200px;">
                                <canvas id="employeeStatusChart"></canvas>
                            </div>
                        </div>

                        <!-- Actions Card -->
                        <div class="card border bg-light">
                            <div class="card-body">
                                <h6 class="mb-3">إجراءات سريعة</h6>
                                <div class="row g-2">
                                    <div class="col-md-6">
                                        <a href="{% url 'Hr:employees:create' %}?department={{ department.dept_code }}" class="btn btn-success btn-sm d-block">
                                            <i class="fas fa-user-plus me-1"></i> إضافة موظف
                                        </a>
                                    </div>
                                    <div class="col-md-6">
<a href="{% url 'Hr:employees:list' %}?department={{ department.dept_code }}" class="btn btn-info btn-sm d-block">
    <i class="fas fa-users me-1"></i> الموظفين
</a>
                                    </div>
                                    <div class="col-md-6">
<a href="{% url 'Hr:reports:report_detail' 'department_report' %}?department={{ department.dept_code }}" class="btn btn-primary btn-sm d-block">
    <i class="fas fa-file-alt me-1"></i> تقرير القسم
</a>
                                    </div>
                                    <div class="col-md-6">
                                        <a href="{% url 'Hr:departments:performance' department.dept_code %}" class="btn btn-warning btn-sm d-block">
                                            <i class="fas fa-chart-line me-1"></i> أداء القسم
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Employees Tab Card -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light py-3 d-flex justify-content-between align-items-center">
                <h5 class="mb-0 fw-semibold">
                    <i class="fas fa-users text-primary me-2"></i>موظفو القسم
                    <span class="badge bg-primary rounded-pill ms-2">{{ employee_count }}</span>
                </h5>
                <a href="{% url 'Hr:employees:create' %}?department={{ department.dept_code }}" class="btn btn-success btn-sm">
                    <i class="fas fa-user-plus me-1"></i> إضافة موظف
                </a>
            </div>
            <div class="card-body p-0">
                {% if employees %}
                <div class="table-responsive">
                    <table class="table table-hover align-middle mb-0">
                        <thead class="table-light">
                            <tr>
                                <th class="py-3 px-4">الرقم</th>
                                <th class="py-3 px-4">الاسم</th>
                                <th class="py-3 px-4">المسمى الوظيفي</th>
                                <th class="py-3 px-4">تاريخ التعيين</th>
                                <th class="py-3 px-4">الحالة</th>
                                <th class="py-3 px-4 text-center">العمليات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for employee in employees %}
                            <tr>
                                <td class="px-4">{{ employee.emp_id }}</td>
                                <td class="px-4">
                                    <div class="d-flex align-items-center">
                                        {% if employee.emp_image %}
                                        <img src="{{ employee.emp_image|binary_to_img }}" alt="{{ employee.emp_full_name }}" class="rounded-circle me-2 object-fit-cover" width="40" height="40">
                                        {% else %}
                                        <div class="avatar-sm bg-secondary text-white me-2">{{ employee.emp_first_name|slice:":1"|upper }}</div>
                                        {% endif %}
                                        <div>
                                            <a href="{% url 'Hr:employees:detail' employee.emp_id %}" class="fw-medium d-block text-decoration-none text-dark">{{ employee.emp_full_name|default:employee.emp_first_name }}</a>
                                            <small class="text-muted">{{ employee.national_id|default:'' }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-4">{{ employee.jop_name|default:"-" }}</td>
                                <td class="px-4">{{ employee.emp_date_hiring|date:"Y-m-d"|default:"-" }}</td>
                                <td class="px-4">
                                    {% if employee.working_condition == 'سارى' %}
                                    <span class="badge bg-success">سارى</span>
                                    {% elif employee.working_condition == 'انقطاع عن العمل' %}
                                    <span class="badge bg-info">انقطاع عن العمل</span>
                                    {% elif employee.working_condition == 'استقالة' %}
                                    <span class="badge bg-danger">استقالة</span>
                                    {% else %}
                                    <span class="badge bg-secondary">{{ employee.working_condition|default:"-" }}</span>
                                    {% endif %}
                                </td>
                                <td class="px-4 text-center">
                                    <div class="btn-group btn-group-sm">
                                        <a href="{% url 'Hr:employees:detail' employee.emp_id %}" class="btn btn-outline-primary" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{% url 'Hr:employees:edit' employee.emp_id %}" class="btn btn-outline-secondary" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                {% if is_paginated %}
                <div class="pagination-container p-3 border-top">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}"><i class="fas fa-chevron-right"></i></a>
                        </li>
                        {% endif %}

                        {% for num in page_obj.paginator.page_range %}
                        {% if num <= page_obj.number|add:2 and num >= page_obj.number|add:-2 %}
                        <li class="page-item {% if num == page_obj.number %}active{% endif %}">
                            <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                        </li>
                        {% endif %}
                        {% endfor %}

                        {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}"><i class="fas fa-chevron-left"></i></a>
                        </li>
                        {% endif %}
                    </ul>
                </div>
                {% endif %}

                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-users fa-3x mb-3 text-muted"></i>
                    <p class="mb-0">لا يوجد موظفين مسجلين في هذا القسم حالياً.</p>
                    <a href="{% url 'Hr:employees:create' %}?department={{ department.dept_code }}" class="btn btn-primary mt-3">
                        <i class="fas fa-user-plus me-1"></i> إضافة موظف جديد
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteModalLabel">تأكيد الحذف</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف القسم: <span id="itemName" class="fw-bold"></span>؟</p>
                <p class="text-danger mb-0"><i class="fas fa-exclamation-triangle me-1"></i> سيؤدي هذا إلى إزالة القسم وجميع البيانات المرتبطة به. هذا الإجراء لا يمكن التراجع عنه.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <a href="#" id="confirmDelete" class="btn btn-danger">تأكيد الحذف</a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .avatar {
        width: 60px;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        font-size: 1.5rem;
        font-weight: bold;
    }

    .avatar-sm {
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        font-size: 1rem;
        font-weight: bold;
    }

    .object-fit-cover {
        object-fit: cover;
    }

    .chart-container {
        position: relative;
    }

    @media (min-width: 768px) {
        .border-sm-end {
            border-right: 1px solid #dee2e6;
        }
    }
</style>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Handle delete modal
        const deleteModal = document.getElementById('deleteModal');
        if (deleteModal) {
            deleteModal.addEventListener('show.bs.modal', function(event) {
                const button = event.relatedTarget;
                const itemName = button.getAttribute('data-name');
                const url = button.getAttribute('data-url');

                document.getElementById('itemName').textContent = itemName;
                document.getElementById('confirmDelete').setAttribute('href', url);
            });
        }

        // Employee Status Chart
        const statusCtx = document.getElementById('employeeStatusChart').getContext('2d');
        const statusData = {
            labels: ['سارى', 'انقطاع عن العمل', 'استقالة' , 'أخرى'],
            datasets: [{
                data: [{{ active_count }}, {{ on_leave_count }}, {{ resigned_count }}, {{ other_count }}],
                backgroundColor: ['#28a745', '#17a2b8', '#dc3545', '#6c757d'],
                borderWidth: 1
            }]
        };

        new Chart(statusCtx, {
            type: 'pie',
            data: statusData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            font: {
                                size: 12
                            }
                        }
                    }
                }
            }
        });
    });
</script>
{% endblock %}
