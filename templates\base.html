{% load static %}
<!DOCTYPE html>
<html lang="{{ current_language }}" dir="{{ text_direction }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}ElDawliya System{% endblock %}</title>
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- ElDawliya Design System -->
    <link rel="stylesheet" href="{% static 'css/style.css' %}">
    <link rel="stylesheet" href="{% static 'css/theme-toggle.css' %}">
    <!-- RTL CSS -->
    <link rel="stylesheet" href="{% static 'css/rtl.css' %}">

    <!-- Theme Detection Script (Inline for immediate execution) -->
    <script>
        // Prevent flash of unstyled content
        (function() {
            const theme = localStorage.getItem('eldawliya-theme') ||
                         (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
            document.documentElement.setAttribute('data-theme', theme);
        })();
    </script>
    <!-- Google Fonts -->
    {% if system_settings.font_family == 'cairo' %}
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    {% elif system_settings.font_family == 'tajawal' %}
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;800&display=swap" rel="stylesheet">
    {% elif system_settings.font_family == 'almarai' %}
    <link href="https://fonts.googleapis.com/css2?family=Almarai:wght@300;400;700;800&display=swap" rel="stylesheet">
    {% elif system_settings.font_family == 'ibm-plex-sans-arabic' %}
    <link href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    {% elif system_settings.font_family == 'noto-sans-arabic' %}
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    {% else %}
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    {% endif %}
    
    <style>
        :root {
            --font-family: 
                {% if system_settings.font_family == 'cairo' %}'Cairo'
                {% elif system_settings.font_family == 'tajawal' %}'Tajawal'
                {% elif system_settings.font_family == 'almarai' %}'Almarai'
                {% elif system_settings.font_family == 'ibm-plex-sans-arabic' %}'IBM Plex Sans Arabic'
                {% elif system_settings.font_family == 'noto-sans-arabic' %}'Noto Sans Arabic'
                {% else %}'Cairo'{% endif %}, sans-serif;
        }

        body {
            font-family: var(--font-family), system-ui, -apple-system, sans-serif;
            text-align: right;
        }

        /* RTL specific styles */
        .dropdown-menu {
            text-align: right;
        }

        .form-check {
            padding-right: 1.5em;
            padding-left: 0;
        }

        .form-check .form-check-input {
            float: right;
            margin-right: -1.5em;
            margin-left: 0;
        }

        .input-group > :not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(.valid-feedback):not(.invalid-tooltip):not(.invalid-feedback) {
            margin-right: -1px;
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
            border-top-left-radius: 0.375rem;
            border-bottom-left-radius: 0.375rem;
        }

        .input-group:not(.has-validation) > :not(:last-child):not(.dropdown-toggle):not(.dropdown-menu):not(.form-floating) {
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
            border-top-right-radius: 0.375rem;
            border-bottom-right-radius: 0.375rem;
        }
    </style>
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Modern Navigation Bar -->
    <nav class="navbar navbar-expand-lg">
        <div class="container-fluid">
            <a class="navbar-brand d-flex align-items-center" href="{% url 'home_dashboard' %}">
                <div class="sidebar-brand-icon me-3">
                    <i class="fas fa-building"></i>
                </div>
                <div>
                    <div class="sidebar-brand-title">نظام الدولية</div>
                    <div class="sidebar-brand-subtitle text-xs">إدارة متكاملة</div>
                </div>
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav"
                    aria-controls="navbarNav" aria-expanded="false" aria-label="تبديل التنقل">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'meetings:list' %}">
                            <i class="fas fa-users me-2"></i>الاجتماعات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'tasks:list' %}">
                            <i class="fas fa-tasks me-2"></i>المهام
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user me-2"></i>{{ user.get_full_name|default:user.username }}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-user-cog me-2"></i>الملف الشخصي</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-2"></i>الإعدادات</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'accounts:logout' %}"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Enhanced Messages -->
    <div class="container mt-4">
        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-{% if message.tags == 'error' %}error{% elif message.tags == 'warning' %}warning{% elif message.tags == 'success' %}success{% else %}info{% endif %} alert-dismissible fade show" role="alert">
                    <div class="alert-icon">
                        {% if message.tags == 'error' %}
                            <i class="fas fa-exclamation-circle"></i>
                        {% elif message.tags == 'warning' %}
                            <i class="fas fa-exclamation-triangle"></i>
                        {% elif message.tags == 'success' %}
                            <i class="fas fa-check-circle"></i>
                        {% else %}
                            <i class="fas fa-info-circle"></i>
                        {% endif %}
                    </div>
                    <div class="alert-content">
                        <div class="alert-message">{{ message }}</div>
                    </div>
                    <button type="button" class="alert-close btn-close" data-bs-dismiss="alert" aria-label="إغلاق">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            {% endfor %}
        {% endif %}
    </div>

    <!-- Main Content -->
    <div class="container mt-4">
        {% block content %}
        {% endblock %}
    </div>

    <!-- Modern Footer -->
    <footer class="mt-8 py-6" style="background: linear-gradient(135deg, var(--secondary-800) 0%, var(--secondary-900) 100%); color: var(--white);">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <div class="d-flex align-items-center mb-3">
                        <div class="sidebar-brand-icon me-3">
                            <i class="fas fa-building"></i>
                        </div>
                        <div>
                            <h5 class="mb-1 text-white">نظام الدولية إنترناشونال</h5>
                            <p class="mb-0 text-sm opacity-75">حلول إدارية متكاملة</p>
                        </div>
                    </div>
                    <p class="text-sm opacity-75">&copy; {{ "now"|date:"Y" }} نظام الدولية إنترناشونال للطباعة. جميع الحقوق محفوظة.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <div class="mb-3">
                        <a href="#" class="text-white text-decoration-none me-4 hover:text-primary-300">
                            <i class="fas fa-shield-alt me-1"></i>سياسة الخصوصية
                        </a>
                        <a href="#" class="text-white text-decoration-none hover:text-primary-300">
                            <i class="fas fa-file-contract me-1"></i>شروط الاستخدام
                        </a>
                    </div>
                    <p class="text-sm opacity-75">
                        مدعوم بواسطة <a href="https://www.eldawliya.com" class="text-primary-300 text-decoration-none font-medium">ElDawliya</a>
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Toastify JS -->
    <script src="https://cdn.jsdelivr.net/npm/toastify-js"></script>
    <!-- ElDawliya Theme System -->
    <script src="{% static 'js/theme-toggle.js' %}"></script>
    <!-- Custom JavaScript -->
    <script>
        // Apply RTL specific JavaScript adjustments
        document.addEventListener('DOMContentLoaded', function() {
            // Set RTL for all elements that need it
            document.querySelectorAll('.dropdown-menu').forEach(menu => {
                menu.style.textAlign = 'right';
            });

            // Adjust data tables for RTL if you're using them
            if (typeof $.fn.DataTable !== 'undefined') {
                $.extend(true, $.fn.DataTable.defaults, {
                    language: {
                        url: '//cdn.datatables.net/plug-ins/1.10.24/i18n/Arabic.json'
                    }
                });
            }

            // Fix any RTL issues with third-party plugins
            document.querySelectorAll('.fc-header-toolbar').forEach(toolbar => {
                toolbar.style.direction = 'rtl';
            });

            // Initialize tooltips with RTL support
            const tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]');
            if (tooltipTriggerList.length > 0) {
                tooltipTriggerList.forEach(tooltipTriggerEl => {
                    new bootstrap.Tooltip(tooltipTriggerEl, {
                        placement: 'auto',
                        container: 'body'
                    });
                });
            }
        });
    </script>
    {% block extra_js %}{% endblock %}
</body>
</html>
