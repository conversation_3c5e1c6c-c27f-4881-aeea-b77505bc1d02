{% load static %}
{% load i18n %}
{% load image_utils %}
<!DOCTYPE html>
<html lang="{{ current_language|default:'ar' }}" dir="{{ text_direction|default:'rtl' }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="ElDawliya HR - Modern Human Resources Management System">
    <title>{% block title %}نظام إدارة الموارد البشرية - الدولية{% endblock %}</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{% static 'images/favicon.ico' %}">

    <!-- Google Fonts - Cairo Primary -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Modern HR Design System -->
    <link rel="stylesheet" href="{% static 'Hr/css/hr_clean.css' %}">

    {% block extra_css %}{% endblock %}
</head>
<body>
    <div class="app-container">
        <!-- Sidebar Navigation -->
        <aside class="sidebar" id="sidebar">
            <!-- Brand Section -->
            <div class="sidebar-brand">
                <div class="sidebar-brand-icon">
                    <i class="fas fa-building"></i>
                </div>
                <div class="sidebar-brand-text">
                    <h1 class="sidebar-brand-title">الدولية</h1>
                    <p class="sidebar-brand-subtitle">الموارد البشرية</p>
                </div>
            </div>

            <!-- Navigation Menu -->
            <nav class="nav-menu">
                <ul class="nav-menu">
                    <li class="nav-item">
                        <a href="{% url 'Hr:dashboard' %}" class="nav-link {% if request.resolver_match.url_name == 'dashboard' %}active{% endif %}">
                            <i class="nav-icon fas fa-tachometer-alt"></i>
                            <span class="nav-text">لوحة التحكم</span>
                        </a>
                    </li>

                    <li class="nav-item">
                        <a href="{% url 'Hr:employees:list' %}" class="nav-link {% if 'employees' in request.resolver_match.namespace %}active{% endif %}">
                            <i class="nav-icon fas fa-users"></i>
                            <span class="nav-text">إدارة الموظفين</span>
                        </a>
                    </li>

                    <li class="nav-item">
                        <a href="{% url 'Hr:employees:create' %}" class="nav-link">
                            <i class="nav-icon fas fa-user-plus"></i>
                            <span class="nav-text">إضافة موظف</span>
                        </a>
                    </li>

                    <li class="nav-item">
                        <a href="{% url 'Hr:departments:list' %}" class="nav-link {% if 'departments' in request.resolver_match.namespace %}active{% endif %}">
                            <i class="nav-icon fas fa-sitemap"></i>
                            <span class="nav-text">الأقسام</span>
                        </a>
                    </li>

                    <li class="nav-item">
                        <a href="{% url 'Hr:jobs:list' %}" class="nav-link {% if 'jobs' in request.resolver_match.namespace %}active{% endif %}">
                            <i class="nav-icon fas fa-briefcase"></i>
                            <span class="nav-text">الوظائف</span>
                        </a>
                    </li>

                    <li class="nav-item">
                        <a href="{% url 'attendance:dashboard' %}" class="nav-link">
                            <i class="nav-icon fas fa-clock"></i>
                            <span class="nav-text">الحضور والانصراف</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content Area -->
        <div class="main-content">
            <!-- Top Navigation Bar -->
            <nav class="navbar">
                <div class="navbar-left">
                    <button class="mobile-menu-toggle" id="mobileMenuToggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <div>
                        <h1 class="navbar-title">{% block page_title %}نظام الموارد البشرية{% endblock %}</h1>
                        <p class="navbar-subtitle">{% block page_subtitle %}إدارة شاملة للموارد البشرية{% endblock %}</p>
                    </div>
                </div>

                <div class="navbar-right">
                    <!-- Theme Toggle -->
                    <button id="themeToggle" class="theme-toggle" title="تبديل الوضع الليلي/النهاري">
                        <i id="themeIcon" class="fas fa-moon"></i>
                    </button>

                    <!-- User Info -->
                    <div class="navbar-user">
                        <div class="navbar-user-avatar">
                            {{ request.user.get_full_name.0|default:request.user.username.0|upper }}
                        </div>
                        <div class="navbar-user-info">
                            <p class="navbar-user-name">{{ request.user.get_full_name|default:request.user.username }}</p>
                            <p class="navbar-user-role">مدير النظام</p>
                        </div>
                    </div>

                    <!-- Logout Button -->
                    <a href="{% url 'accounts:logout' %}" class="btn btn-danger btn-sm">
                        <i class="fas fa-sign-out-alt"></i>
                        <span class="d-none d-md-inline">تسجيل الخروج</span>
                    </a>
                </div>
            </nav>

            <!-- Content Container -->
            <div class="content-container">
                {% block content %}{% endblock %}
            </div>
        </div>
    </div>
    <!-- Theme Manager Script -->
    <script src="{% static 'Hr/js/theme-manager.js' %}"></script>

    <!-- Mobile Menu Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const mobileMenuToggle = document.getElementById('mobileMenuToggle');
            const sidebar = document.getElementById('sidebar');

            if (mobileMenuToggle && sidebar) {
                mobileMenuToggle.addEventListener('click', function() {
                    sidebar.classList.toggle('show');
                });

                // Close sidebar when clicking outside on mobile
                document.addEventListener('click', function(e) {
                    if (window.innerWidth <= 991) {
                        if (!sidebar.contains(e.target) && !mobileMenuToggle.contains(e.target)) {
                            sidebar.classList.remove('show');
                        }
                    }
                });

                // Close sidebar on window resize to desktop
                window.addEventListener('resize', function() {
                    if (window.innerWidth > 991) {
                        sidebar.classList.remove('show');
                    }
                });
            }
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
